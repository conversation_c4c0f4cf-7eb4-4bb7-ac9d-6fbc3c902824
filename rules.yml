id: generate_categories_crud_pages
name: Generate Categories CRUD Pages
description: |
  Generate CRUD pages for categories in the React frontend using the existing API endpoints in the products app,
  and using the bootstrap 'react-bootstrap' library for styling.
rules:
  - name: Create Categories List Page
    description: |
      Create a page to display all categories in a table with pagination, sorting, and search functionality.
      Include buttons for creating, viewing, editing, and deleting categories.
    requirements:
      - Use react-bootstrap Table component for displaying categories
      - Implement pagination with 10 items per page
      - Add search functionality to filter categories by name
      - Include sorting by name and creation date
      - Add loading states and error handling
      - Use react-router for navigation

  - name: Create Category Form Component
    description: |
      Create a reusable form component for creating and editing categories.
    requirements:
      - Use react-bootstrap Form components
      - Include validation for required fields
      - Support both create and edit modes
      - Show appropriate success/error messages
      - Handle form submission with proper loading states

  - name: Implement Category Creation
    description: |
      Create functionality to add new categories.
    requirements:
      - Connect to the POST /categories/ endpoint
      - Show success message on successful creation
      - Redirect to categories list after successful creation
      - Handle and display validation errors

  - name: Implement Category Viewing
    description: |
      Create a page to view category details.
    requirements:
      - Connect to the GET /categories/{id}/ endpoint
      - Display all category details in a clean layout
      - Include a back button to return to the list
      - Show loading and error states

  - name: Implement Category Editing
    description: |
      Create functionality to edit existing categories.
    requirements:
      - Pre-fill the form with existing category data
      - Connect to the PUT /categories/{id}/ endpoint
      - Show success message on update
      - Redirect to categories list after successful update
      - Handle and display validation errors

  - name: Implement Category Deletion
    description: |
      Create functionality to delete categories with confirmation.
    requirements:
      - Show confirmation dialog before deletion
      - Connect to the DELETE /api/categories/{id}/ endpoint
      - Show success message after deletion
      - Refresh the categories list after deletion
      - Handle errors appropriately

  - name: Error Handling and Loading States
    description: |
      Ensure proper error handling and loading states throughout the application.
    requirements:
      - Show loading spinners during API calls
      - Display user-friendly error messages
      - Handle network errors gracefully
      - Implement proper error boundaries

  - name: Styling and Responsiveness
    description: |
      Ensure the UI is responsive and follows the application's design system.
    requirements:
      - Use react-bootstrap components consistently
      - Ensure mobile responsiveness
      - Follow the application's color scheme and styling
      - Include proper spacing and typography

  - name: Code Organization
    description: |
      Maintain clean and organized code structure.
    requirements:
      - Create separate components for list, form, and view
      - Use custom hooks for API calls
      - Implement proper prop types
      - Add meaningful comments and documentation
      - Follow React best practices

  - name: Testing
    description: |
      Ensure the functionality is properly tested.
    requirements:
      - Write unit tests for components
      - Test form validation
      - Test API integration
      - Test error scenarios
      - Ensure good test coverage