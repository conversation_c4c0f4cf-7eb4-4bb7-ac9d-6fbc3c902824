from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class Employee(TimeStampedModel):
    class EmployeeType(models.TextChoices):
        FULL_TIME = "full_time", _("Full Time")
        PART_TIME = "part_time", _("Part Time")
        DAILY = "daily", _("Daily Worker")

    class Status(models.TextChoices):
        ACTIVE = "active", _("Active")
        INACTIVE = "inactive", _("Inactive")
        TERMINATED = "terminated", _("Terminated")

    user = models.OneToOneField(
        "users.User", on_delete=models.CASCADE, verbose_name="User"
    )
    address = models.TextField()
    type = models.CharField(
        max_length=10, choices=EmployeeType.choices, default=EmployeeType.DAILY
    )
    hour_rate = models.DecimalField(max_digits=10, decimal_places=2)
    day_rate = models.DecimalField(max_digits=10, decimal_places=2)
    hire_date = models.DateField()
    status = models.CharField(
        max_length=10, choices=Status.choices, default=Status.ACTIVE
    )
    pos = models.ForeignKey(
        "pos.POS",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("POS"),
        help_text=_("The POS terminal this employee works at"),
    )
    identification = models.ImageField(upload_to="identification/")

    class Meta:
        ordering = ["-created"]
        verbose_name = "Employee"
        verbose_name_plural = "Employees"

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} - {self.user.role} - {self.pos.name if self.pos else 'No POS'}"
