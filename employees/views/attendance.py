import decimal

from django.contrib.contenttypes.models import ContentType
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from pos.models.pos_session import POSSession
from pos.models.pos_session_transaction import POSSessionTransaction, TransactionType
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly

from ..models import Attendance, Salary
from ..serializers.attendance import (
    AttendanceSerializer,
    TimeInSerializer,
    TimeOutSerializer,
)


class AttendanceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    View for employees to record their attendance.
    Requires username, password, and an image of the employee.
    """

    queryset = Attendance.objects.all()
    serializer_class = AttendanceSerializer
    pagination_class = PaginationClass
    filter_backends = [Djan<PERSON>FilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["employee"]
    search_fields = [
        "pos__name",
        "employee__user__first_name",
        "employee__user__last_name",
    ]
    ordering_fields = ["time_in"]
    ordering = ["time_in"]
    parser_classes = (MultiPartParser,)

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["list", "retrieve"]:
            permission_classes = [IsAdminOnly]
        else:
            permission_classes = [AllowAny]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        request_body=TimeInSerializer,
        responses={
            200: openapi.Response(
                description="Successful time-in",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, description="Status"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, description="Message"
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "attendance_id": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Attendance ID",
                                ),
                                "employee": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Employee name",
                                ),
                                "time_in": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Time in",
                                ),
                            },
                        ),
                    },
                ),
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="time_in")
    def time_in(self, request, *args, **kwargs):
        serializer = TimeInSerializer(data=request.data)
        if serializer.is_valid():
            try:
                attendance = serializer.save()
                return Response(
                    {
                        "status": "success",
                        "message": "Time-in recorded successfully",
                        "data": {
                            "attendance_id": attendance.id,
                            "employee": str(attendance.employee),
                            "time_in": attendance.time_in,
                        },
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                return Response(
                    {"status": "error", "message": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        return Response(
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(
        request_body=TimeOutSerializer,
        responses={
            200: openapi.Response(
                description="Successful time-out",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, description="Status"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, description="Message"
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "attendance_id": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Attendance ID",
                                ),
                                "employee": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Employee name",
                                ),
                                "time_in": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Time in",
                                ),
                                "time_out": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Time out",
                                ),
                                "salary": openapi.Schema(
                                    type=openapi.TYPE_NUMBER,
                                    description="Salary amount",
                                ),
                            },
                        ),
                    },
                ),
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="time_out")
    def time_out(self, request, *args, **kwargs):
        serializer = TimeOutSerializer(data=request.data)
        if serializer.is_valid():
            try:
                attendance = serializer.validated_data["attendance"]
                employee = attendance.employee
                serializer.update(attendance, serializer.validated_data)
                attendance.refresh_from_db()
                work_hours = attendance.total_hours
                if 12 >= work_hours >= 11:
                    salary_amount = employee.day_rate
                else:
                    # For part-time or daily workers, use hourly rate
                    salary_amount = employee.hour_rate * decimal.Decimal(
                        str(work_hours)
                    )
                # Create salary record
                salary = Salary.objects.create(
                    employee=employee,
                    attendance=attendance,
                    salary=salary_amount,
                    notes=f"Auto-generated for attendance #{attendance.id}",
                )

                # Find open POS session for the employee's POS
                if employee.pos:
                    try:
                        open_session = POSSession.objects.get(
                            pos=employee.pos,
                            status=POSSession.Status.OPEN,
                            closed_at__isnull=True,
                        )

                        # Create POS transaction for the salary
                        POSSessionTransaction.objects.create(
                            session=open_session,
                            transaction_type=TransactionType.SALARY,
                            amount=salary_amount,
                            content_type=ContentType.objects.get_for_model(salary),
                            object_id=salary.id,
                            description=f"Salary for {employee} - {work_hours:.2f} hours",
                        )
                    except POSSession.DoesNotExist:
                        # Log that no open session was found
                        pass

                return Response(
                    {
                        "status": "success",
                        "message": "Time-out recorded successfully",
                        "data": {
                            "attendance_id": attendance.id,
                            "employee": str(attendance.employee),
                            "time_in": attendance.time_in,
                            "time_out": attendance.time_out,
                            "salary": salary.salary,
                        },
                    },
                    status=status.HTTP_200_OK,
                )
            except Exception as e:
                return Response(
                    {"status": "error", "message": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        return Response(
            {"status": "error", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )
