from decimal import Decimal

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from users.models import User

from .pos import POS


class POSSession(TimeStampedModel):
    """
    POS Session model representing a working session for a POS terminal.
    Tracks all transactions and balances during a session.
    """

    class Status(models.TextChoices):
        OPEN = "open", _("Open")
        CLOSED = "closed", _("Closed")
        SUSPENDED = "suspended", _("Suspended")

    pos = models.ForeignKey(
        POS,
        on_delete=models.CASCADE,
        related_name="sessions",
        verbose_name=_("POS device"),
        help_text=_("The POS terminal this session belongs to"),
    )

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="pos_sessions",
        verbose_name=_("user"),
        help_text=_("The user who opened the session"),
    )

    opening_balance = models.DecimalField(
        _("opening balance"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Amount of money at the beginning of the session"),
    )

    closing_balance = models.DecimalField(
        _("closing balance"),
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Amount of money at the end of the session"),
    )

    total_sales = models.DecimalField(
        _("total sales"),
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text=_("Total sales amount during the session"),
    )

    total_expenses = models.DecimalField(
        _("total expenses"),
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text=_("Total expenses during the session"),
    )
    difference = models.DecimalField(
        _("difference"),
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text=_("Total difference between calculated balance and closing balance"),
    )
    opened_at = models.DateTimeField(
        _("opened at"), auto_now_add=True, help_text=_("When the session was opened")
    )

    closed_at = models.DateTimeField(
        _("closed at"),
        null=True,
        blank=True,
        help_text=_("When the session was closed"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about the session"),
    )

    status = models.CharField(
        _("status"),
        max_length=20,
        choices=Status.choices,
        default=Status.OPEN,
        help_text=_("Current status of the session"),
    )

    class Meta:
        verbose_name = _("POS session")
        verbose_name_plural = _("POS sessions")
        ordering = ["-opened_at"]

    def __str__(self):
        return f"{self.pos.name} - {self.get_status_display()} ({self.opened_at})"

    @property
    def calculated_balance(self):
        """Calculate the current balance based on transactions"""
        # Calculate total income (sales, cash in, etc.)
        return (
            Decimal(self.opening_balance)
            + Decimal(self.total_sales)
            - Decimal(self.total_expenses)
        )

    def close_session(self, closing_balance):
        """Close the current session"""
        if self.status != self.Status.OPEN:
            raise Exception(_("Only open sessions can be closed"))

        self.status = self.Status.CLOSED
        self.closed_at = timezone.now()
        self.closing_balance = Decimal(closing_balance)
        self.difference = self.calculated_balance - self.closing_balance
        self.save(
            update_fields=[
                "status",
                "closed_at",
                "closing_balance",
                "modified",
                "difference",
            ]
        )

    def suspend_session(self):
        """Suspend the current session"""
        if self.status != self.Status.OPEN:
            raise Exception(_("Only open sessions can be suspended"))

        self.status = self.Status.SUSPENDED
        self.save(update_fields=["status", "modified"])

    def resume_session(self):
        """Resume a suspended session"""
        if self.status != self.Status.SUSPENDED:
            raise Exception(_("Only suspended sessions can be resumed"))

        self.status = self.Status.OPEN
        self.save(update_fields=["status", "modified"])
