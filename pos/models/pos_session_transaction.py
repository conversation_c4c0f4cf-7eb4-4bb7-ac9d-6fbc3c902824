from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from .pos_session import POSSession


class TransactionType(models.TextChoices):
    SALE = "sale", _("Sale")
    REFUND = "refund", _("Refund")
    PURCHASE = "purchase", _("Purchase")
    REFUND_PURCHASE = "refund_purchase", _("Refund Purchase")
    CASH_IN = "cash_in", _("Cash In")
    CASH_OUT = "cash_out", _("Cash Out")
    EXPENSE = "expense", _("Expense")
    SALARY = "salary", _("Salary")
    OTHER = "other", _("Other")


class POSSessionTransaction(TimeStampedModel):
    """
    POS Session Transaction model representing financial transactions
    that occur during a POS session.
    """

    session = models.ForeignKey(
        POSSession,
        on_delete=models.CASCADE,
        related_name="transactions",
        verbose_name=_("session"),
        help_text=_("The POS session this transaction belongs to"),
    )

    transaction_type = models.CharField(
        _("transaction type"),
        max_length=20,
        choices=TransactionType.choices,
        help_text=_("Type of the transaction"),
    )

    amount = models.DecimalField(
        _("amount"), max_digits=12, decimal_places=2, help_text=_("Transaction amount")
    )

    # ContentType and object_id for the generic relation
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_("Content type of the related object"),
    )
    object_id = models.PositiveIntegerField(
        null=True, blank=True, help_text=_("ID of the related object")
    )
    related_object = GenericForeignKey("content_type", "object_id")

    description = models.TextField(
        _("description"),
        blank=True,
        null=True,
        help_text=_("Additional information about the transaction"),
    )

    class Meta:
        verbose_name = _("POS session transaction")
        verbose_name_plural = _("POS session transactions")
        ordering = ["-created"]

    def __str__(self):
        return f"{self.get_transaction_type_display()}: {self.amount} - {self.created}"

    def save(self, **kwargs):
        """
        Custom save method to handle transaction effects on the session
        """
        is_new = self.pk is None
        super().save(**kwargs)

        if is_new:
            self.update_session_totals()

    def update_session_totals(self):
        """
        Update the session's totals based on this transaction
        """

        # Update session's total_sales or total_expenses based on transaction type
        if self.transaction_type in [
            TransactionType.SALE,
            TransactionType.CASH_IN,
            TransactionType.REFUND_PURCHASE,
        ]:
            self.session.total_sales = self.session.total_sales + self.amount

        if self.transaction_type in [
            TransactionType.EXPENSE,
            TransactionType.CASH_OUT,
            TransactionType.REFUND,
            TransactionType.PURCHASE,
            TransactionType.SALARY,
        ]:
            self.session.total_expenses = self.session.total_expenses + self.amount

        self.session.save(update_fields=["total_sales", "total_expenses", "modified"])
