import React from 'react';
import {BrowserRouter as Router, Navigate, Route, Routes} from 'react-router-dom';
import {Container} from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

import {AuthProvider, useAuth} from './contexts/AuthContext';

import Navigation from './components/layout/Navigation';
import Login from './components/auth/Login';
import Home from "./components/Home";

// Categories components
import CategoriesList from './components/categories/CategoriesList';
import CategoryCreate from './components/categories/CategoryCreate';
import CategoryEdit from './components/categories/CategoryEdit';
import CategoryView from './components/categories/CategoryView';

const ProtectedRoute = ({children, requiredRole}) => {
    const {currentUser, loading} = useAuth();

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!currentUser) {
        return <Navigate to="/login"/>;
    }
     if (requiredRole && currentUser.role !== requiredRole) {
         if (currentUser.role === 'admin' || currentUser.role === 'manager') {
            return children;
        }
        return <Navigate to="/"/>;
    }

    return children;
};

const AuthRedirect = () => {
    const {currentUser, loading} = useAuth();

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!currentUser) {
        return <Navigate to="/login"/>;
    }
    if (currentUser.role === 'admin') {
        return <Navigate to="/admin/dashboard"/>;
    } else if (currentUser.role === 'manager') {
        return <Navigate to="/manager/dashboard"/>;
    }
    return <Navigate to="/cashier/dashboard"/>;
};

function App() {
    return (
        <AuthProvider>
             <Router>
                <Navigation />
                <Container className="mt-4">
                    <Routes>
                        <Route path="/login" element={<Login/>}/>

                        <Route path="/" element={<Home/>}/>

                        {/* Categories Routes */}
                        <Route
                            path="/categories"
                            element={
                                <ProtectedRoute>
                                    <CategoriesList />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/create"
                            element={
                                <ProtectedRoute requiredRole="admin">
                                    <CategoryCreate />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/:id"
                            element={
                                <ProtectedRoute>
                                    <CategoryView />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/:id/edit"
                            element={
                                <ProtectedRoute requiredRole="admin">
                                    <CategoryEdit />
                                </ProtectedRoute>
                            }
                        />

                        <Route path="*" element={<Navigate to="/"/>}/>
                    </Routes>
                </Container>
            </Router>
        </AuthProvider>
    );
}

export default App;
