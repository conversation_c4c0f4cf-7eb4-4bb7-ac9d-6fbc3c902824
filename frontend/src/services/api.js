import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('access');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

export const authService = {
    login: (username, password) =>
        api.post('/auth/login/', {username, password}),
    refresh: (refres) =>
        api.post('/auth/refresh/'),

    getCurrentUser: () =>
        api.get('/auth/me/'),
};

export default api;
