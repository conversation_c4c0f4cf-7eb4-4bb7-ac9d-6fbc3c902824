"""
Django settings for elquds_erp project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import environ

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Initialize environ
env = environ.Env(DEBUG=(bool, False))
# Reading .env file
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env.str("SECRET_KEY", "")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG")

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "jazzmin",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third-party apps
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "drf_yasg",
    "corsheaders",
    # Local apps
    "users",
    "pos",
    "products",
    "accounts",
    "invoices",
    "expenses",
    "purchases",
    "invoices_return",
    "purchases_return",
    "warehouses",
    "employees",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",  # CORS middleware
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# CORS settings
CORS_ALLOWED_ORIGINS = ["http://localhost:3001", "http://127.0.0.1:3001"]
CORS_ALLOW_CREDENTIALS = True

# For production, you might want to use something like:
# CORS_ALLOWED_ORIGINS = [
#     "https://yourdomain.com",
#     "http://localhost:3000",
# ]

ROOT_URLCONF = "elquds_erp.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "elquds_erp.wsgi.application"

AUTH_USER_MODEL = "users.User"
# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "HOST": env.str("POSTGRES_HOST", "127.0.0.1"),
        "PORT": env.str("POSTGRES_PORT", "5432"),
        "NAME": env.str("POSTGRES_DB", ""),
        "USER": env.str("POSTGRES_USER", ""),
        "PASSWORD": env.str("POSTGRES_PASSWORD", ""),
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Jazzmin Configuration
JAZZMIN_SETTINGS = {
    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_title": "Elquds ERP Admin",
    # Title on the login screen (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_header": "Elquds ERP",
    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_brand": "Elquds ERP",
    # Make sidebar collapsible
    "navigation_expanded": False,  # Start with all apps collapsed
    "hide_models": [],  # Hide specific models from the sidebar
    "show_sidebar": True,  # Show the sidebar
    "sidebar_collapse": True,  # Allow sidebar to be toggled
    # Logo to use for your site, must be present in static files, used for brand on top left
    # "site_logo": "img/logo.png",
    # CSS classes that are applied to the logo above
    "site_logo_classes": "img-circle",
    # Welcome text on the login screen
    "welcome_sign": "Welcome to Elquds ERP Admin",
    # Copyright on the footer
    "copyright": "Elquds ERP",
    # The model admin to search from the search bar, search bar omitted if excluded
    "search_model": "auth.User",
    # Field name on user model that contains avatar ImageField/URLField/Charfield or a callable that receives the user
    "user_avatar": None,
    # Links to put along the top menu
    "topmenu_links": [
        # Url that gets reversed (Permissions can be added)
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
        # external url that opens in a new window (Permissions can be added)
        # {"name": "Support", "url": "https://github.com/farridav/django-jazzmin/issues", "new_window": True},
        # model admin to link to (Permissions checked against model)
        {"model": "auth.User"},
    ],
    # Custom icons for side menu apps/models See https://fontawesome.com/icons?d=gallery&m=free&v=5.0.0,5.0.1,5.0.10,5.0.11,5.0.12,5.0.13,5.0.2,5.0.3,5.0.4,5.0.5,5.0.6,5.0.7,5.0.8,5.0.9,5.1.0,5.1.1,5.2.0,5.3.0,5.3.1,5.4.0,5.4.1,5.4.2,5.13.0,5.12.0,5.11.2,5.11.1,5.10.0,5.9.0,5.8.2,5.8.1,5.7.2,5.7.1,5.7.0,5.6.3,5.5.0,5.4.2
    # for the full list of 5.13.0 free icon classes
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "employees.employee": "fas fa-user-tie",
        "employees.attendance": "fas fa-calendar-check",
        "employees.salary": "fas fa-money-bill-wave",
        "products.product": "fas fa-box",
        "products.category": "fas fa-tags",
        "warehouses.warehouse": "fas fa-warehouse",
        "warehouses.stockitem": "fas fa-boxes",
        "warehouses.inventorycount": "fas fa-clipboard-check",
        "warehouses.inventorycountitem": "fas fa-clipboard-list",
        "pos.pos": "fas fa-cash-register",
        "pos.possession": "fas fa-receipt",
        "pos.possessiontransaction": "fas fa-exchange-alt",
        "expenses.expense": "fas fa-file-invoice-dollar",
        "expenses.expensecategory": "fas fa-tags",
    },
    # Icons that are used when one is not manually specified
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",
    #################
    # Related Modal #
    #################
    # Use modals instead of popups
    "related_modal_active": True,
    #############
    # UI Tweaks #
    #############
    # Relative paths to custom CSS/JS scripts (must be present in static files)
    "custom_css": None,
    "custom_js": None,
    # Whether to show the UI customizer on the sidebar
    "show_ui_builder": True,
    # Sidebar styling
    "sidebar_nav_flat_style": False,  # Set to False for collapsible accordion style
    "sidebar_nav_compact": False,  # Make the sidebar items more compact
    "sidebar_nav_legacy_style": False,  # Use modern style for the sidebar
    "sidebar_nav_child_indent": True,  # Add indentation to child items
    "sidebar_fixed": True,  # Fix the sidebar position
    "sidebar_nav_auto_expand": False,  # Don't auto-expand all items, let user control
    "default_collapse_sidebar": True,  # Start with sidebar collapsed
    "sidebar_collapse_icons": {
        "0": "fas fa-chevron-circle-down",
        "1": "fas fa-chevron-circle-right",
    },
    "sidebar_nav_accordion": True,  # Enable accordion functionality
    "sidebar_disable_expand": True,  # Allow expanding/collapsing
    ###############
    # Change view #
    ###############
    # Render out the change view as a single form, or in tabs, current options are
    # - single
    # - horizontal_tabs (default)
    # - vertical_tabs
    # - collapsible
    # - carousel
    "changeform_format": "horizontal_tabs",
    # override change forms on a per modeladmin basis
    "changeform_format_overrides": {
        "users": "collapsible",
        "products": "collapsible",
        "auth.group": "vertical_tabs",
    },
    # Add a language dropdown into the admin
    "language_chooser": False,
}

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STORAGE_TYPE = env.str("STORAGE_TYPE", "LOCAL")
STATIC_URL = "static/"
MEDIA_URL = "media/"
ASSETS_DIR = BASE_DIR / "static"
STATIC_DIR = BASE_DIR / "staticfiles"
MEDIA_DIR = BASE_DIR / "media"
MEDIA_ROOT = BASE_DIR / "media"
STATIC_ROOT = STATIC_DIR
STATICFILES_DIRS = (ASSETS_DIR,)


# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    # Throttling settings
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "60/hour",  # 60 requests per hour for anonymous users
        "user": "1000/day",  # 1000 requests per day for authenticated users
        "auth": "20/hour",  # 20 requests per hour for authentication endpoints
        "analytics": "100/day",  # 100 requests per day for analytics endpoints
    },
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 20,
}

# Swagger
SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Basic": {"type": "basic"},
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"},
    }
}


# JWT Settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": True,
    "AUTH_HEADER_TYPES": ("Bearer", "JWT"),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
}
